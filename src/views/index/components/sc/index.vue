<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-21 16:31:44
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240809/20240809.png)'"> -->
    <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240430-2.png)'"> -->
    <div class="bannerRoll">
      <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20240912/banner.png)'"> -->
      <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240407.png)'"> -->
      <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/activity/20240527-01.png)'"> -->
      <!-- <div class="bannerRoll" :style="'background-image: url(https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/active/sc-20240606/banner.png)'"> -->
      <NavHeight bgc="rgba(0,0,0,0)" />
      <!-- 头部 -->
      <Nav />
      <!-- 搜索 -->
      <Search v-if="isShow" />
      <!-- banner -->
      <Banner v-if="isShow" />
      <!-- 分类 -->
      <Classification v-if="isShow&&isLoading" />

      <div class="nhj_btn" @click="goToNHJ" />

      <Banner2 v-if="true" />

    </div>

    <Classification v-if="isLoading" />

    <!-- 公告 -->
    <News v-if="false" />

    <!-- 中部广告 -->
    <Advertise v-if="false" />

    <!-- ccb广告 -->
    <CCB v-if="false" />

    <!-- 拼团活动 -->
    <Pt />

    <Banner />

    <!-- 天天特价/限时抢购 -->
    <Actieve />

    <!-- 优选店铺 -->
    <Optimization />

    <!-- 附近的店 -->
    <NearbyShop />

    <!-- 优惠券弹出 -->
    <CouponDialog v-if="isCouponShow" :type="limitObject" :list="couponList" @couponPickAll="couponPickAll" @offCoupon="offCoupon" @noticeRefresh="noticeRefresh" />

    <div style="height:100px" />
    <TwoCardUser v-if="userAccountCheck" />
    <Nhj v-if="ifOpen" @funIfOpen="funIfOpen" />

  </div>
</template>

<script>
import { isCcbUnionPay, findTbSkinHomePage, findTbSkinStartUp } from '@/api/index'
import { couponNotice, noticeRefresh, couponPickAll } from '@/api/coupon'
import { initGetVirtualPhone } from '@/api/login'
import {
  nmyUrl
} from '@/config/die'

import Nav from './components/nav'
import TwoCardUser from '@/components/Privacy/twoCardUser.vue'
import Search from './components/search'
import Banner from './components/banner'
import Classification from './components/classification'
import News from './components/news'
import Actieve from './components/actieve'
import Optimization from './components/optimization'
import NearbyShop from './components/nearbyShop'
import Advertise from './components/advertise'
import Pt from './components/pt'
import CouponDialog from '@/components/Coupon/DiaLog/index.vue'

import Nhj from '@/components/Advert/open.vue'

// ccb广告
import CCB from '@/modules/CCB/index.vue'

import Banner2 from './components/banner2'

import logger from '@/utils/aliLog'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Classification,
    News,
    Actieve,
    Optimization,
    NearbyShop,
    Advertise,
    TwoCardUser,
    Pt,
    CouponDialog,
    CCB,
    Nhj,
    Banner2
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex,
      ifOpen: false,
      ifPtAd: false,
      userAccountCheck: false,
      isCouponShow: false,
      limitObject: 0,
      couponList: [],
      couponIdList: [],
      isLoading: false,
      isShow: false,
      // urls: 'https://emallh5.namek.com.cn/emall/pages/index/index?/tenant=scxddwlkjyxzrgs'
      // urls: 'https://emallh5.namek.com.cn/emall/pages/auth/thirdPageIn?appId=9a64f1eda3&tenant=scxddwlkjyxzrgs&targetURL=1001&hideTitle=1&source=2037'
      urls: 'https://emallh5.namek.com.cn/emall/pages/auth/thirdPageIn?appId=9a64f1eda3&tenant=scxddwlkjyxzrgs&targetURL=1001&hideTitle=1&source=2037'
    }
  },
  created() {
    this.findTbSkinHomePage()
    this.$store.state.Index.bannerIndex = 0
    this.$store.state.tabbar.index = 0
    // this.isCcbUnionPay()
    this.couponNotice()
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    this.startAppInfo()

    // if (localStorage.getItem('advertopen19') == '1') {
    //   this.ifOpen = false
    // } else {
    //   this.ifOpen = true
    // }
    logger.sum('遂昌-日活')

    // if (localStorage.getItem('ccbtips') != '1') {
    //   this.$dialog.alert({
    //     title: '公告',
    //     message: '本次二类户余额显示为0原因是建行端的余额查询接口出现问题，待银联接口对接恢复后二类户余额方可显示，用户无需担心资金风险问题。造成的不便，请谅解。'
    //   }).then(() => {
    //     localStorage.setItem('ccbtips', '1')
    //   })
    // }
  },
  methods: {
    async goToNHJ() {
      // this.$router.push({
      //   path: '/activity-zqj'
      // })
      // return

      // this.$router.push({
      //   path: '/Classify',
      //   query: { name: '外卖', cateId: 0, isTakeaway: true }
      // })
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'PwdLogin' })
      //   return
      // }

      // -----------------------------------

      const { status, message } = await initGetVirtualPhone({
        platId: 5
      })
      if (status === 401) {
        this.$toast('请先登录')
        return
      }
      if (status !== 200) {
        this.$toast('获取号码失败，请稍后再试')
        return
      }

      let userNo = this.$store.getters.getUserNo

      let statusHeight = this.$store.getters.getStatusHeight

      let url = nmyUrl + '&statusHeight=' + statusHeight + '&navigationBarHeight=' + this.$store.getters.getNavigationBarHeight + '&userNo=' + this.signNmy(userNo) + '&phone=' + this.signNmy(message)

      console.log(url)

      AlipayJSBridge.call('pushWindow', {
        url: url,
        param: {
          readTitle: true,
          showOptionMenu: false,
          transparentTitle: 'always'
        },
        passData: {}
      })
    },
    // 关闭活动入口
    funIfOpen() {
      localStorage.setItem('advertopen19', 1)
      this.ifOpen = false
      localStorage.removeItem('advertopen18')
    },
    // 启动图
    startAppInfo() {
      findTbSkinStartUp(1).then(res => {
        if (res.status === 200) {
          console.log(res.data)
          if (localStorage.getItem('startAppInfo') != res.data.startUpSkin) {
            localStorage.setItem('startAppInfo', res.data.startUpSkin)
            AlipayJSBridge.call('startAppInfo', { imgUrl: res.data.startUpSkin, typetime: res.data.startUpSkin }, function(result) {
              console.log('APP启动图：' + result)
            })
          }
        }
      })
    },
    // 首次进去优惠券列表
    couponNotice() {
      couponNotice(this.$store.getters.getRegionId).then(res => {
        if (res.status == 200 && res.data !== null && res.data.length > 0) {
          if (res.data.length === 1) {
            this.limitObject = res.data[0].limitObject
          } else {
            this.limitObject = 0
          }

          let cdata = res.data
          let newData = []
          for (let i = 0; i < cdata.length; i++) {
            newData.push(...cdata[i].couponNoticeDetailList)
          }
          this.couponList = newData
          this.isCouponShow = true
          for (let i = 0; i < this.couponList.length; i++) {
            this.couponIdList.push(this.couponList[i].id)
          }
        }
      })
    },
    // 领完后刷新优惠券列表
    noticeRefresh() {
      noticeRefresh(this.couponIdList).then(res => {
        if (res.status == 200 && res.data !== null && res.data.length > 0) {
          let cdata = res.data
          let newData = []
          for (let i = 0; i < cdata.length; i++) {
            newData.push(...cdata[i].couponNoticeDetailList)
          }
          this.couponList = newData
        }
      })
    },
    // 全部领取
    couponPickAll() {
      couponPickAll(this.couponIdList).then(res => {
        if (res.status === 200) {
          this.$toast('领取成功')
          this.noticeRefresh()
          this.$router.push({
            path: '/Classify',
            query: { name: '外卖', cateId: 0, isTakeaway: true }
          })
        }
      })
    },
    offCoupon() {
      this.isCouponShow = false
    },
    isCcbUnionPay() {
      isCcbUnionPay().then(res => {
        if (res.status == 200) {
          if (res.data.popupFalg === true && sessionStorage.getItem('userAccountCheck') != 1) {
            this.userAccountCheck = true
          }
        }
      })
    },
    // 查询皮肤信息
    findTbSkinHomePage() {
      findTbSkinHomePage().then(res => {
        if (res.status == 200) {
          this.$store.state.Index.template = res.data
          this.isLoading = true
        }
      })
    },
    // 加密
    signNmy(val) {
      const crypto = require('crypto')

      // 加密秘钥和iv，可以自行更改
      const secretKey = '92f33d7ce63483f5'
      const iv = '572019c19836877d'

      const cipher = crypto.createCipheriv('aes-128-cbc', secretKey, iv)

      let encrypted = cipher.update(val, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      return encrypted
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
  width: 100%;
  .advertiseZq{
    width: 730px;
    height: 260px;
    margin: 0 auto;
    margin-top: 24px;
    img{
        width: 100%;
        height: 100%;
    }
  }
  .bannerRoll{
    width: 100%;
    height: 850px;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .nhj_btn{
      width: 100%;
      height: 600px;
      position: absolute;
      bottom: 30px;
    }
  }
}
</style>
