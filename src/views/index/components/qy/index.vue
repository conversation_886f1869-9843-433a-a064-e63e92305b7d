<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2022-09-09 14:40:22
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="bannerRoll">
      <NavHeight bgc="rgba(0,0,0,0)" />
      <Nav />
      <Search v-if="false" />
      <Banner v-if="false" />

      <div class="nhj_btn" @click="getPhone" />

      <Banner2 v-if="true" />
    </div>
    <Classification />

    <!-- 活动 -->
    <Actieve />

    <!-- 附近的店 -->
    <NearbyShop />
    <!-- <div>
      <div class="advertise">
        <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/active/20240318-03.png" alt="">
      </div>
    </div> -->

    <div style="height:150px" />
  </div>
</template>

<script>

import Nav from './components/nav'
import Search from './components/search'
import Banner from './components/banner'
import Classification from './components/classification'
import NearbyShop from './components/nearbyShop'
import Actieve from './components/actieve'
import Banner2 from './components/banner2'

import logger from '@/utils/aliLog'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Classification,
    NearbyShop,
    Actieve,
    Banner2
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex
    }
  },
  created() {},
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    logger.sum('庆元-日活')
  },
  methods: {
    // 拨打电话
    getPhone() {
      console.log('拨打电话')
      AlipayJSBridge.call(
        'CallPhone',
        {
          phoneNum: '0578-6035366'
        },
        function(result) { }
      )
      // this.$router.push({
      //   path: '/market/details',
      //   query: {
      //     id: '119'
      //   }
      // })
      // this.$router.push('/market/home')
    }
  }
}
</script>

<style lang="scss" scoped>
.home{
  width: 100%;
  .advertise{
    width: 710px;
    margin: 0 auto;
    margin-top: 30px;
    img{
        width: 100%;
        height: 100%;
    }
  }
  .bannerRoll{
    width: 100%;
    height: 1020px;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .nhj_btn{
      width: 100%;
      height: 700px;
      position: absolute;
      bottom: 50px;
    }
  }
}
</style>
