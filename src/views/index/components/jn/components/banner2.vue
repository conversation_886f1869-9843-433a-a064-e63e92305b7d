<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-29 11:34:50
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-10 09:22:53
-->
<template>
  <div class="banner2" :style="{ top: topVal }">
    <van-swipe class="banner" :autoplay="20000" touchable indicator-color="#74E165" @change="onChange">
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <img
          class="bannerImg"
          :src="item.image + '?x-oss-process=image/resize,w_700/format,jpg/quality,q_99'"
          @click="goActieve(item)"
        >
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // list: this.$store.state.Index.banner
      list: [
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250402/qingming.png',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0508/banner.jpg',
        //   url: '99'
        // },
        // {
        //   image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/2025/0604/20250604-1.jpg',
        //   url: '99'
        // },
        {
          image: 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/banner/20250402/banner.png',
          url: '20241202'
        }
      ],
      isShowCouponList: [],
      topVal: '0px'
    }
  },
  created() {
    // 获取屏幕高度
    let topVal = Number(this.$store.getters.getStatusHeight) + 40 + 'px'
    this.topVal = '-' + topVal
    console.log(this.topVal)
  },
  methods: {
    // 跳转
    async goActieve(val) {
      if (val.url == '20241202') {
        AlipayJSBridge.call(
          'CallPhone',
          {
            phoneNum: '13587187911'
          },
          function(result) { }
        )
      }
    },

    onChange(index) {
      this.$store.state.Index.bannerIndex = index
    },
    // 判断当前登录的手机号是否在数组中
    isShowCoupon() {
      if (this.isShowCouponList.length == 0) {
        return true
      }
      let phone = this.$store.getters.getPhone
      let isShow = false
      this.isShowCouponList.forEach(item => {
        if (item == phone) {
          isShow = true
        }
      })
      return isShow
    },
    // 加密
    signNmy(val) {
      const crypto = require('crypto')

      // 加密秘钥和iv，可以自行更改
      const secretKey = '92f33d7ce63483f5'
      const iv = '572019c19836877d'

      const cipher = crypto.createCipheriv('aes-128-cbc', secretKey, iv)

      let encrypted = cipher.update(val, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      return encrypted
    }
  }
}
</script>

<style>
.van-image-preview__close-icon--top-right {
  top: 100px;
  font-size: 60px;
}
</style>

<style lang="scss" scoped>
.banner2 {
  position: relative;
  z-index: 1;
  border-bottom-right-radius: 30px;
    border-bottom-left-radius:30px;

  ::v-deep .van-swipe__indicator {
    width: 25px;
    height: 3px;
  }

  .banner {
    width: 750px;
    height: 1000px;
    margin: 0 auto;
    border-bottom-right-radius: 30px;
    border-bottom-left-radius:30px;
  }

  .bannerImg {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
}
</style>
