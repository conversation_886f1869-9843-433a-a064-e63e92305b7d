<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-05-25 14:22:27
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-07-28 10:19:54
-->
<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="bannerRoll">
      <NavHeight bgc="rgba(0,0,0,0)" />
      <!-- 头部 -->
      <Nav />
      <!-- 搜索 -->
      <Search v-if="isShow" />
      <!-- banner -->
      <Banner v-if="isShow" />
      <!-- 分类 -->
      <Classification v-if="isShow && isLoading" />

      <div class="nhj_btn" @click="goToNHJ" />

      <Banner2 v-if="true" />

    </div>

    <!-- 分类 -->
    <Classification />

    <!-- 中部广告 -->
    <Advertise />

    <!-- 天天特价/限时抢购 -->
    <Actieve v-if="false" />

    <!-- 招商 -->
    <!-- <Living /> -->

    <!-- 附近的店 -->
    <NearbyShop />

    <div style="height:100px" />

  </div>
</template>

<script>
import Nav from './components/nav'
import Search from './components/search'
import Banner from './components/banner'
import Classification from './components/classification'
import Advertise from './components/advertise'
import Banner2 from './components/banner2'
// import Living from './components/living'

import Actieve from './components/actieve'
import NearbyShop from './components/nearbyShop'

import { findTbSkinStartUp } from '@/api/index'

import logger from '@/utils/aliLog'

export default {
  components: {
    Nav,
    Search,
    Banner,
    Classification,
    Actieve,
    NearbyShop,
    Advertise,
    Banner2
  },
  data() {
    return {
      bannerIndex: this.$store.state.Index.bannerIndex,
      ifOpen: false,
      tipsStatus: false,
      bulletinShow: false,
      isShow: false
    }
  },
  created() {
    this.startAppInfo()
    this.$store.state.Index.bannerIndex = 0
    this.$store.state.tabbar.index = 0
  },
  mounted() {
    // 回到顶部
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0

    if (this.is0728() && localStorage.getItem('a20230728') != '1') {
      this.bulletinShow = true
    }

    logger.sum('景宁-日活')
  },
  methods: {
    // 启动图
    startAppInfo() {
      findTbSkinStartUp(3).then(res => {
        if (res.status === 200) {
          if (res.data) {
            if (localStorage.getItem('startAppInfo') != res.data.startUpSkin) {
              localStorage.setItem('startAppInfo', res.data.startUpSkin)
              AlipayJSBridge.call('startAppInfo', { imgUrl: res.data.startUpSkin, typetime: res.data.startUpSkin }, function(result) {
                console.log('APP启动图：' + result)
              })
            }
          }
        }
      })
    },
    imUrl(row) {
      return 'https://diandi-app.oss-cn-hangzhou.aliyuncs.com/appV3/home/<USER>/background.png'
    },
    close() {
      this.tipsStatus = false
    },
    bulletinFun() {
      this.bulletinShow = false
      localStorage.setItem('a20230728', '1')
    },
    is0728() {
      // 创建当前日期的Date对象
      var today = new Date()

      // 获取当天的年、月、日
      var year = today.getFullYear()
      var month = today.getMonth() + 1 // 月份从0开始，需要加1
      var day = today.getDate()

      // 将年、月、日转为字符串格式，并格式化成八位数，比较是否和指定日期相等
      var currentDate = year.toString() +
        (month < 10 ? '0' + month.toString() : month.toString()) +
        (day < 10 ? '0' + day.toString() : day.toString())

      // 判断是否是指定日期
      if (currentDate === '20230728') {
        return true
      } else {
        return false
      }
    },
    goToNHJ() {
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  width: 100%;

  .bannerRoll{
    width: 100%;
    height: 1000px;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .nhj_btn{
      width: 100%;
      height: 600px;
      position: absolute;
      bottom: 30px;
    }
  }
}
</style>
