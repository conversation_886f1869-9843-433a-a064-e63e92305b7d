<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: 孙立政
 * @Date: 2021-06-21 15:19:36
 * @LastEditors: 孙立政
 * @LastEditTime: 2023-08-11 17:39:50
-->
<template>
  <div class="home">

    <div class="adImgs" @click="goActive">
      <img src="https://diandi-app.oss-cn-hangzhou.aliyuncs.com/store/sm/202505/20250610-1.jpg" alt="">
    </div>

    <div class="close" @click="close">
      <van-icon class="close_icon" name="close" size="30" color="#fff" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {},
  methods: {
    goActive() {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) // ios终端
      this.close()
      let userNo = this.$store.getters.getUserNo
      let phone = this.$store.getters.getPhone
      let query = ''
      if (userNo != null || phone != null && userNo != '' && phone != '') {
        query = `&query=userNo%3D${userNo}%26phone%3D${phone}`
      }
      let urls = `weixin://dl/business/?appid=wxf72e75902d97af48&path=pages/goods/list-sm` + query
      if (!isiOS) {
        window.location.href = urls
      } else {
        AlipayJSBridge.call('OpenAppByRouter', {
          urlStr: urls
        }, function(result) {})
      }
      // this.$toast('欢迎进店消费')
      // if (this.$store.getters.getUserId == null) {
      //   this.$router.push({ name: 'wxLogin2' })
      //   return
      // }
      // this.$router.push({ name: 'ActivityQixi' })
      // this.close()
      // this.$router.push({
      //   name: 'SetMeal',
      //   query: {
      //     id: 210,
      //     type: 2
      //   }
      // })
    },
    close() {
      this.$emit('funIfOpen')
    }
  }
}
</script>
<style scoped lang="scss">
    .home {
        width: 100%;
        height: 100vh;
        background-color: rgba(0,0,0,.8);
        position: fixed;
        top: 0;
        z-index: 1002;
        .adImgs{
            width: 650px;
            height: 740px;
            margin: 0 auto;
            margin-top: 28%;
            img{
              width: 650px;
              height:740px;
              border-radius: 10px;
            }
        }
        .close{
            width: 100%;
            text-align: center;
            margin-top: 20px;

            .close_icon{
              // margin-right: 85px;
            }
        }
    }
</style>
